// 最佳实践：统一处理连续和非连续数据
use std::collections::HashMap;

// 数据点配置结构
#[derive(Debug, <PERSON>lone)]
struct DataPoint {
    name: String,
    tag_name: String,
    data_type: DataType,
    count: u32,  // 1表示单个点位，>1表示数组
    gateway: String,
    plc_path: String,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
enum DataType {
    Float32,
    Int16,
    UInt16,
    Bool,
}

impl DataType {
    fn size(&self) -> u32 {
        match self {
            DataType::Float32 => 4,
            DataType::Int16 | DataType::UInt16 => 2,
            DataType::Bool => 1,
        }
    }
}

// 统一的数据采集器
struct UnifiedDataCollector {
    data_points: Vec<DataPoint>,
    // tags: HashMap<String, RawTag>,  // 实际使用时取消注释
}

impl UnifiedDataCollector {
    fn new() -> Self {
        Self {
            data_points: Vec::new(),
            // tags: HashMap::new(),
        }
    }

    // 添加数据点（不管是单个还是数组）
    fn add_data_point(&mut self, data_point: DataPoint) {
        println!("添加数据点: {} ({}个元素)", data_point.name, data_point.count);
        
        // 构建标签路径
        let path = format!(
            "protocol=ab-eip&plc=controllogix&path={}&gateway={}&name={}&elem_count={}&elem_size={}",
            data_point.plc_path,
            data_point.gateway,
            data_point.tag_name,
            data_point.count,
            data_point.data_type.size()
        );
        
        println!("标签路径: {}", path);
        
        // 实际使用时：
        // let tag = RawTag::new(&path, 1000)?;
        // self.tags.insert(data_point.name.clone(), tag);
        
        self.data_points.push(data_point);
    }

    // 统一的数据读取方法
    fn read_all_data(&self) -> HashMap<String, Vec<f64>> {
        let mut results = HashMap::new();
        
        for data_point in &self.data_points {
            println!("读取数据点: {}", data_point.name);
            
            // 实际使用时：
            // if let Some(tag) = self.tags.get(&data_point.name) {
            //     if tag.read(1000).is_ok() {
            //         let mut values = Vec::new();
            //         let elem_size = data_point.data_type.size() as usize;
            //         
            //         for i in 0..data_point.count {
            //             let offset = i as usize * elem_size;
            //             if let Ok(value) = tag.get_value::<f32>(offset) {
            //                 values.push(value as f64);
            //             }
            //         }
            //         
            //         results.insert(data_point.name.clone(), values);
            //     }
            // }
            
            // 模拟数据（实际使用时删除这部分）
            let mut mock_values = Vec::new();
            for i in 0..data_point.count {
                mock_values.push(20.0 + i as f64 * 0.5); // 模拟温度数据
            }
            results.insert(data_point.name.clone(), mock_values);
        }
        
        results
    }

    // 数据处理（统一处理单个和数组数据）
    fn process_data(&self, data: HashMap<String, Vec<f64>>) {
        for (name, values) in data {
            if values.len() == 1 {
                // 单个数据点
                println!("单点 {}: {:.2}", name, values[0]);
                
                // 你的业务逻辑：比如检查报警阈值
                if values[0] > 100.0 {
                    println!("⚠️  {} 超过阈值！", name);
                }
            } else {
                // 数组数据
                let avg = values.iter().sum::<f64>() / values.len() as f64;
                let max = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
                let min = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
                
                println!("数组 {}: 平均={:.2}, 最大={:.2}, 最小={:.2}", name, avg, max, min);
                
                // 你的业务逻辑：比如检查数组中的异常值
                for (i, &value) in values.iter().enumerate() {
                    if (value - avg).abs() > 10.0 {
                        println!("⚠️  {}[{}] 数值异常: {:.2}", name, i, value);
                    }
                }
            }
        }
    }

    // 开始采集循环
    fn start_polling(&self) {
        println!("开始数据采集...");
        
        // 实际使用时这里是无限循环
        for cycle in 1..=3 {  // 模拟3次采集
            println!("\n=== 采集周期 {} ===", cycle);
            
            let data = self.read_all_data();
            self.process_data(data);
            
            // std::thread::sleep(std::time::Duration::from_millis(1000));
        }
    }
}

fn main() {
    let mut collector = UnifiedDataCollector::new();
    
    // 添加单个数据点
    collector.add_data_point(DataPoint {
        name: "主反应器温度".to_string(),
        tag_name: "MainReactor_Temp".to_string(),
        data_type: DataType::Float32,
        count: 1,  // 单个点位
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
    });
    
    collector.add_data_point(DataPoint {
        name: "进料压力".to_string(),
        tag_name: "Inlet_Pressure".to_string(),
        data_type: DataType::Float32,
        count: 1,  // 单个点位
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
    });
    
    // 添加数组数据点
    collector.add_data_point(DataPoint {
        name: "温度监测阵列".to_string(),
        tag_name: "TempArray".to_string(),
        data_type: DataType::Float32,
        count: 8,  // 8个温度点
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
    });
    
    collector.add_data_point(DataPoint {
        name: "压力传感器组".to_string(),
        tag_name: "PressureGroup".to_string(),
        data_type: DataType::Float32,
        count: 5,  // 5个压力点
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
    });
    
    // 开始采集
    collector.start_polling();
}

/*
关键要点总结：

1. **统一配置**: 用count参数区分单个(count=1)和数组(count>1)
2. **统一创建**: 都用相同的RawTag::new()方法
3. **统一读取**: 都用tag.read()然后get_value()
4. **统一处理**: 在process_data中根据values.len()判断如何处理

这样你就不需要写两套代码了！
*/
