use plctag::{RawTag, Result as PlcResult, ValueExt};
use std::collections::HashMap;
use std::thread;
use std::time::{Duration, Instant};

/// 数据点类型
#[derive(Debug, Clone)]
pub enum DataPointType {
    /// 单个数据点
    Single { data_type: DataType },
    /// 连续数组数据
    Array { data_type: DataType, count: u32 },
}

/// 支持的数据类型
#[derive(Debug, Clone)]
pub enum DataType {
    Float32,
    Int16,
    UInt16,
    Int32,
    UInt32,
    Bool,
}

impl DataType {
    fn size(&self) -> u32 {
        match self {
            DataType::Float32 => 4,
            DataType::Int32 | DataType::UInt32 => 4,
            DataType::Int16 | DataType::UInt16 => 2,
            DataType::Bool => 1,
        }
    }
}

/// 数据点配置
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct DataPointConfig {
    pub name: String,
    pub tag_name: String,
    pub point_type: DataPointType,
    pub gateway: String,
    pub plc_path: String,
}

/// 优化的PLC数据采集器
pub struct OptimizedPlcCollector {
    /// 所有标签的映射
    tags: HashMap<String, RawTag>,
    /// 数据点配置
    configs: HashMap<String, DataPointConfig>,
    /// 采集间隔
    poll_interval: Duration,
}

impl OptimizedPlcCollector {
    pub fn new(poll_interval_ms: u64) -> Self {
        Self {
            tags: HashMap::new(),
            configs: HashMap::new(),
            poll_interval: Duration::from_millis(poll_interval_ms),
        }
    }

    /// 添加数据点配置
    pub fn add_data_point(&mut self, config: DataPointConfig) -> PlcResult<()> {
        let path = self.build_tag_path(&config)?;
        let tag = RawTag::new(&path, 1000)?;
        
        println!("添加数据点: {} -> {} ({:?})", 
                config.name, config.tag_name, config.point_type);
        
        self.tags.insert(config.name.clone(), tag);
        self.configs.insert(config.name.clone(), config);
        
        Ok(())
    }

    /// 构建标签路径
    fn build_tag_path(&self, config: &DataPointConfig) -> PlcResult<String> {
        let (elem_count, elem_size) = match &config.point_type {
            DataPointType::Single { data_type } => (1, data_type.size()),
            DataPointType::Array { data_type, count } => (*count, data_type.size()),
        };

        Ok(format!(
            "protocol=ab-eip&plc=controllogix&path={}&gateway={}&name={}&elem_count={}&elem_size={}",
            config.plc_path, config.gateway, config.tag_name, elem_count, elem_size
        ))
    }

    /// 读取所有数据点
    pub fn read_all_data(&self) -> HashMap<String, DataValue> {
        let mut results = HashMap::new();
        
        for (name, tag) in &self.tags {
            if let Some(config) = self.configs.get(name) {
                match tag.read(1000) {
                    Ok(_) => {
                        if let Some(value) = self.extract_value(tag, config) {
                            results.insert(name.clone(), value);
                        }
                    }
                    Err(e) => {
                        eprintln!("读取数据点 {} 失败: {:?}", name, e);
                    }
                }
            }
        }
        
        results
    }

    /// 从标签中提取值
    fn extract_value(&self, tag: &RawTag, config: &DataPointConfig) -> Option<DataValue> {
        match &config.point_type {
            DataPointType::Single { data_type } => {
                self.read_single_value(tag, data_type, 0).map(DataValue::Single)
            }
            DataPointType::Array { data_type, count } => {
                let mut values = Vec::new();
                let elem_size = data_type.size() as usize;
                
                for i in 0..*count {
                    let offset = i as usize * elem_size;
                    if let Some(value) = self.read_single_value(tag, data_type, offset) {
                        values.push(value);
                    }
                }
                
                if !values.is_empty() {
                    Some(DataValue::Array(values))
                } else {
                    None
                }
            }
        }
    }

    /// 读取单个值
    fn read_single_value(&self, tag: &RawTag, data_type: &DataType, offset: usize) -> Option<f64> {
        match data_type {
            DataType::Float32 => tag.get_value::<f32>(offset).ok().map(|v| v as f64),
            DataType::Int16 => tag.get_value::<i16>(offset).ok().map(|v| v as f64),
            DataType::UInt16 => tag.get_value::<u16>(offset).ok().map(|v| v as f64),
            DataType::Int32 => tag.get_value::<i32>(offset).ok().map(|v| v as f64),
            DataType::UInt32 => tag.get_value::<u32>(offset).ok().map(|v| v as f64),
            DataType::Bool => tag.get_value::<bool>(offset).ok().map(|v| if v { 1.0 } else { 0.0 }),
        }
    }

    /// 开始数据采集
    pub fn start_polling<F>(&self, mut data_handler: F) 
    where 
        F: FnMut(HashMap<String, DataValue>),
    {
        println!("开始优化的PLC数据采集，采集间隔: {:?}", self.poll_interval);
        
        loop {
            let start_time = Instant::now();
            
            // 读取所有数据
            let data = self.read_all_data();
            
            // 调用数据处理回调
            data_handler(data);
            
            let elapsed = start_time.elapsed();
            println!("本次采集耗时: {:?}", elapsed);
            
            // 等待下次采集
            if elapsed < self.poll_interval {
                thread::sleep(self.poll_interval - elapsed);
            }
        }
    }
}

/// 数据值枚举
#[derive(Debug, Clone)]
pub enum DataValue {
    Single(f64),
    Array(Vec<f64>),
}

impl DataValue {
    pub fn as_single(&self) -> Option<f64> {
        match self {
            DataValue::Single(v) => Some(*v),
            _ => None,
        }
    }

    pub fn as_array(&self) -> Option<&Vec<f64>> {
        match self {
            DataValue::Array(v) => Some(v),
            _ => None,
        }
    }
}

/// 示例使用函数
pub fn run_advanced_example() -> PlcResult<()> {
    let mut collector = OptimizedPlcCollector::new(1000);
    
    let gateway = "192.168.1.100";
    let plc_path = "1,0";
    
    // 添加单个数据点
    collector.add_data_point(DataPointConfig {
        name: "温度1".to_string(),
        tag_name: "Temperature1".to_string(),
        point_type: DataPointType::Single { data_type: DataType::Float32 },
        gateway: gateway.to_string(),
        plc_path: plc_path.to_string(),
    })?;
    
    // 添加数组数据点
    collector.add_data_point(DataPointConfig {
        name: "温度数组".to_string(),
        tag_name: "TempArray".to_string(),
        point_type: DataPointType::Array { 
            data_type: DataType::Float32, 
            count: 10 
        },
        gateway: gateway.to_string(),
        plc_path: plc_path.to_string(),
    })?;
    
    // 开始采集，使用闭包处理数据
    collector.start_polling(|data| {
        println!("=== 采集到的数据 ===");
        for (name, value) in data {
            match value {
                DataValue::Single(v) => println!("单点 {}: {}", name, v),
                DataValue::Array(arr) => println!("数组 {}: {:?}", name, arr),
            }
        }
        println!("==================");
    });
    
    Ok(())
}
