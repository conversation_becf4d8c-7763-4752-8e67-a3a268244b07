// 长期数据采集最佳实践示例
// 使用 auto_sync_read_ms 实现自动数据采集

use std::collections::HashMap;
use std::thread;
use std::time::{Duration, Instant};

// 模拟 plctag 库的结构（实际使用时取消注释并删除模拟代码）
// use plctag::RawTag;

// 数据点配置
#[derive(Debu<PERSON>, <PERSON>lone)]
struct DataPoint {
    name: String,
    tag_name: String,
    data_type: DataType,
    count: u32,
    gateway: String,
    plc_path: String,
    read_interval_ms: u32, // 读取间隔
    cache_ms: u32,         // 缓存时间
}

#[derive(Debug, Clone)]
enum DataType {
    Float32,
    Int32,
    Int16,
    Bool,
}

impl DataType {
    fn size(&self) -> u32 {
        match self {
            DataType::Float32 | DataType::Int32 => 4,
            DataType::Int16 => 2,
            DataType::Bool => 1,
        }
    }
}

// 模拟的 RawTag（实际使用时删除这个结构）
struct MockRawTag {
    name: String,
    base_value: f32,
    start_time: Instant,
}

impl MockRawTag {
    fn new(path: &str, _timeout: u32) -> Result<Self, String> {
        // 从路径中提取标签名（简化版）
        let name = if let Some(start) = path.find("name=") {
            let name_part = &path[start + 5..];
            if let Some(end) = name_part.find('&') {
                name_part[..end].to_string()
            } else {
                name_part.to_string()
            }
        } else {
            "Unknown".to_string()
        };

        println!("✅ 创建标签: {} (路径: {})", name, path);

        Ok(Self {
            name,
            base_value: 20.0 + (rand::random::<f32>() * 10.0),
            start_time: Instant::now(),
        })
    }

    fn get_f32(&self, _offset: u32) -> Result<f32, String> {
        // 模拟变化的数据
        let elapsed = self.start_time.elapsed().as_secs_f32();
        let variation = (elapsed * 0.1).sin() * 2.0 + (rand::random::<f32>() - 0.5) * 0.5;
        Ok(self.base_value + variation)
    }

    fn get_i32(&self, _offset: u32) -> Result<i32, String> {
        Ok((self.get_f32(0)? * 100.0) as i32)
    }

    fn get_i16(&self, _offset: u32) -> Result<i16, String> {
        Ok(self.get_f32(0)? as i16)
    }

    fn get_bool(&self, _offset: u32) -> Result<bool, String> {
        Ok(self.get_f32(0)? > self.base_value)
    }
}

// 自动数据采集器
struct AutoDataCollector {
    data_points: Vec<DataPoint>,
    tags: HashMap<String, MockRawTag>, // 实际使用: HashMap<String, RawTag>
    last_read_times: HashMap<String, Instant>,
    statistics: CollectionStats,
}

#[derive(Debug, Default)]
struct CollectionStats {
    total_reads: u64,
    successful_reads: u64,
    failed_reads: u64,
    start_time: Option<Instant>,
}

impl AutoDataCollector {
    fn new() -> Self {
        Self {
            data_points: Vec::new(),
            tags: HashMap::new(),
            last_read_times: HashMap::new(),
            statistics: CollectionStats::default(),
        }
    }

    // 添加数据点并创建自动同步标签
    fn add_data_point(&mut self, data_point: DataPoint) -> Result<(), String> {
        println!(
            "🔧 配置数据点: {} ({}个元素, {}ms间隔)",
            data_point.name, data_point.count, data_point.read_interval_ms
        );

        // 构建带自动同步的标签路径
        let path = format!(
            "protocol=ab-eip&plc=controllogix&path={}&gateway={}&name={}&elem_count={}&elem_size={}&auto_sync_read_ms={}&read_cache_ms={}",
            data_point.plc_path,
            data_point.gateway,
            data_point.tag_name,
            data_point.count,
            data_point.data_type.size(),
            data_point.read_interval_ms,
            data_point.cache_ms
        );

        // 创建标签（实际使用时取消注释）
        // let tag = RawTag::new(&path, 5000)?;
        let tag = MockRawTag::new(&path, 5000)?;

        self.tags.insert(data_point.name.clone(), tag);
        self.last_read_times
            .insert(data_point.name.clone(), Instant::now());
        self.data_points.push(data_point);

        Ok(())
    }

    // 读取所有数据点的当前值
    fn read_all_current_values(&mut self) -> HashMap<String, Vec<f64>> {
        let mut results = HashMap::new();

        for data_point in &self.data_points {
            if let Some(tag) = self.tags.get(&data_point.name) {
                self.statistics.total_reads += 1;

                match self.read_data_point_values(data_point, tag) {
                    Ok(values) => {
                        self.statistics.successful_reads += 1;
                        self.last_read_times
                            .insert(data_point.name.clone(), Instant::now());
                        results.insert(data_point.name.clone(), values);
                    }
                    Err(e) => {
                        self.statistics.failed_reads += 1;
                        eprintln!("❌ 读取 {} 失败: {}", data_point.name, e);
                    }
                }
            }
        }

        results
    }

    // 读取单个数据点的所有值
    fn read_data_point_values(
        &self,
        data_point: &DataPoint,
        tag: &MockRawTag,
    ) -> Result<Vec<f64>, String> {
        let mut values = Vec::new();
        let elem_size = data_point.data_type.size() as u32;

        for i in 0..data_point.count {
            let offset = i * elem_size;

            let value = match data_point.data_type {
                DataType::Float32 => tag.get_f32(offset)? as f64,
                DataType::Int32 => tag.get_i32(offset)? as f64,
                DataType::Int16 => tag.get_i16(offset)? as f64,
                DataType::Bool => {
                    if tag.get_bool(offset)? {
                        1.0
                    } else {
                        0.0
                    }
                }
            };

            values.push(value);
        }

        Ok(values)
    }

    // 处理采集到的数据
    fn process_collected_data(&self, data: &HashMap<String, Vec<f64>>) {
        for (name, values) in data {
            if values.len() == 1 {
                // 单个数据点
                let value = values[0];
                println!("📊 {} = {:.2}", name, value);

                // 业务逻辑：检查阈值
                if name.contains("温度") && value > 80.0 {
                    println!("🚨 {} 温度过高: {:.2}°C", name, value);
                } else if name.contains("压力") && value > 50.0 {
                    println!("⚠️  {} 压力异常: {:.2}bar", name, value);
                }
            } else {
                // 数组数据
                let avg = values.iter().sum::<f64>() / values.len() as f64;
                let max = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
                let min = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));

                println!(
                    "📈 {} [{}个点]: 平均={:.2}, 最大={:.2}, 最小={:.2}",
                    name,
                    values.len(),
                    avg,
                    max,
                    min
                );

                // 检查数组中的异常值
                for (i, &value) in values.iter().enumerate() {
                    if (value - avg).abs() > 5.0 {
                        println!(
                            "⚠️  {}[{}] 数值异常: {:.2} (偏离平均值 {:.2})",
                            name,
                            i,
                            value,
                            (value - avg).abs()
                        );
                    }
                }
            }
        }
    }

    // 显示采集统计信息
    fn show_statistics(&self) {
        if let Some(start_time) = self.statistics.start_time {
            let elapsed = start_time.elapsed();
            let success_rate = if self.statistics.total_reads > 0 {
                (self.statistics.successful_reads as f64 / self.statistics.total_reads as f64)
                    * 100.0
            } else {
                0.0
            };

            println!("\n📈 采集统计 (运行时间: {:.1}秒):", elapsed.as_secs_f64());
            println!("   总读取次数: {}", self.statistics.total_reads);
            println!("   成功次数: {}", self.statistics.successful_reads);
            println!("   失败次数: {}", self.statistics.failed_reads);
            println!("   成功率: {:.1}%", success_rate);
        }
    }

    // 开始自动数据采集
    fn start_auto_collection(&mut self, duration_seconds: u64) {
        println!("🚀 开始自动数据采集 (持续{}秒)...\n", duration_seconds);

        self.statistics.start_time = Some(Instant::now());
        let end_time = Instant::now() + Duration::from_secs(duration_seconds);

        let mut cycle_count = 0;

        while Instant::now() < end_time {
            cycle_count += 1;
            println!("=== 采集周期 {} ===", cycle_count);

            // 读取所有数据点
            let data = self.read_all_current_values();

            // 处理数据
            if !data.is_empty() {
                self.process_collected_data(&data);
            } else {
                println!("⚠️  本周期未获取到任何数据");
            }

            // 每10个周期显示一次统计
            if cycle_count % 10 == 0 {
                self.show_statistics();
            }

            println!();

            // 等待下一个周期（这里设置为1秒，实际中可以根据需要调整）
            thread::sleep(Duration::from_millis(1000));
        }

        println!("✅ 数据采集完成");
        self.show_statistics();
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 初始化自动数据采集系统...\n");

    let mut collector = AutoDataCollector::new();

    // 添加高频采集的关键数据点
    collector.add_data_point(DataPoint {
        name: "主反应器温度".to_string(),
        tag_name: "MainReactor_Temp".to_string(),
        data_type: DataType::Float32,
        count: 1,
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
        read_interval_ms: 500, // 500ms高频采集
        cache_ms: 100,
    })?;

    collector.add_data_point(DataPoint {
        name: "进料压力".to_string(),
        tag_name: "Inlet_Pressure".to_string(),
        data_type: DataType::Float32,
        count: 1,
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
        read_interval_ms: 1000, // 1秒中频采集
        cache_ms: 200,
    })?;

    // 添加数组数据点
    collector.add_data_point(DataPoint {
        name: "温度监测阵列".to_string(),
        tag_name: "TempArray".to_string(),
        data_type: DataType::Float32,
        count: 8,
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
        read_interval_ms: 2000, // 2秒低频采集
        cache_ms: 500,
    })?;

    collector.add_data_point(DataPoint {
        name: "系统状态位".to_string(),
        tag_name: "SystemStatus".to_string(),
        data_type: DataType::Bool,
        count: 1,
        gateway: "192.168.1.100".to_string(),
        plc_path: "1,0".to_string(),
        read_interval_ms: 5000, // 5秒状态检查
        cache_ms: 1000,
    })?;

    // 开始30秒的自动采集演示
    collector.start_auto_collection(30);

    Ok(())
}
