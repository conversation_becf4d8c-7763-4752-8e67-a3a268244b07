# PLC数据采集配置文件示例

[plc]
gateway = "*************"
path = "1,0"
poll_interval_ms = 1000
timeout_ms = 1000

# 单个数据点配置
[[single_points]]
name = "温度传感器1"
tag_name = "Temperature1"
data_type = "Float32"
description = "主反应器温度"

[[single_points]]
name = "压力传感器1"
tag_name = "Pressure1"
data_type = "Float32"
description = "主管道压力"

[[single_points]]
name = "流量计1"
tag_name = "Flow1"
data_type = "Float32"
description = "进料流量"

[[single_points]]
name = "液位计1"
tag_name = "Level1"
data_type = "Float32"
description = "储罐液位"

[[single_points]]
name = "电机状态1"
tag_name = "Motor1_Status"
data_type = "Bool"
description = "搅拌电机运行状态"

# 数组数据点配置
[[array_points]]
name = "温度数组"
tag_name = "TempArray"
data_type = "Float32"
count = 10
description = "多点温度监测数组"

[[array_points]]
name = "压力数组"
tag_name = "PressureArray"
data_type = "Float32"
count = 8
description = "管道压力监测数组"

[[array_points]]
name = "流量数组"
tag_name = "FlowArray"
data_type = "Float32"
count = 5
description = "多路流量监测数组"

[[array_points]]
name = "振动数据"
tag_name = "VibrationData"
data_type = "Float32"
count = 100
description = "设备振动频谱数据"

# 数据处理配置
[data_processing]
# 是否启用数据验证
enable_validation = true

# 数据范围验证
[data_processing.validation_ranges]
"温度传感器1" = { min = -50.0, max = 200.0 }
"压力传感器1" = { min = 0.0, max = 10.0 }
"流量计1" = { min = 0.0, max = 1000.0 }
"液位计1" = { min = 0.0, max = 100.0 }

# 数据存储配置
[data_storage]
# 存储类型: "database", "file", "both"
storage_type = "both"

# 数据库配置
[data_storage.database]
host = "localhost"
port = 5432
database = "plc_data"
username = "plc_user"
password = "plc_password"

# 文件存储配置
[data_storage.file]
directory = "./data"
file_format = "csv"  # "csv", "json", "parquet"
rotation_interval = "daily"  # "hourly", "daily", "weekly"

# 报警配置
[alarms]
enable_alarms = true

[[alarms.rules]]
point_name = "温度传感器1"
condition = "greater_than"
threshold = 150.0
message = "温度过高报警"
priority = "high"

[[alarms.rules]]
point_name = "压力传感器1"
condition = "greater_than"
threshold = 8.0
message = "压力过高报警"
priority = "high"

[[alarms.rules]]
point_name = "液位计1"
condition = "less_than"
threshold = 10.0
message = "液位过低报警"
priority = "medium"
