# PLC数据采集系统

基于Rust的PLC数据采集系统，使用`plctag`库实现对Allen-Bradley PLC的数据读取。

## 功能特点

- 支持单个数据点和数组数据的统一处理
- 灵活的数据类型支持（Float32, Int16, UInt16, Int32, UInt32, Bool）
- 可配置的采集间隔
- 错误处理和重连机制
- 支持长期数据采集

## 核心优势

### 统一的数据处理方式

你不需要为连续和非连续数据分别写处理逻辑！本系统提供了统一的接口：

```rust
// 添加单个数据点
collector.add_data_point(DataPointConfig {
    name: "温度1".to_string(),
    tag_name: "Temperature1".to_string(),
    point_type: DataPointType::Single { data_type: DataType::Float32 },
    // ...
});

// 添加数组数据点
collector.add_data_point(DataPointConfig {
    name: "温度数组".to_string(),
    tag_name: "TempArray".to_string(),
    point_type: DataPointType::Array { 
        data_type: DataType::Float32, 
        count: 10 
    },
    // ...
});
```

### 自动数据类型处理

系统会根据配置自动处理不同的数据类型，你只需要关注业务逻辑：

```rust
collector.start_polling(|data| {
    for (name, value) in data {
        match value {
            DataValue::Single(v) => {
                // 处理单个数据点
                println!("单点 {}: {}", name, v);
            }
            DataValue::Array(arr) => {
                // 处理数组数据
                println!("数组 {}: 平均值 = {}", name, 
                    arr.iter().sum::<f64>() / arr.len() as f64);
            }
        }
    }
});
```

## 使用方法

### 1. 基础示例

```bash
cargo run
```

运行基础示例，展示如何读取单个数据点和数组数据。

### 2. 高级示例

查看 `src/advanced_example.rs` 了解更高级的用法：

- 统一的数据点配置
- 灵活的数据类型支持
- 回调函数处理数据

### 3. 配置文件

参考 `config_example.toml` 了解如何通过配置文件管理数据点：

```toml
[plc]
gateway = "192.168.1.100"
path = "1,0"
poll_interval_ms = 1000

[[single_points]]
name = "温度传感器1"
tag_name = "Temperature1"
data_type = "Float32"

[[array_points]]
name = "温度数组"
tag_name = "TempArray"
data_type = "Float32"
count = 10
```

## 解决你的问题

### 问题：连续和非连续数据需要分别处理？

**答案：不需要！**

本系统的核心设计理念就是统一处理。你只需要：

1. **配置阶段**：告诉系统哪些是单点，哪些是数组
2. **处理阶段**：使用统一的接口处理所有数据

### 优势对比

| 传统方式 | 本系统方式 |
|---------|-----------|
| 单点一套代码，数组一套代码 | 统一的配置和处理接口 |
| 需要手动管理不同数据类型 | 自动类型转换和处理 |
| 错误处理分散在各处 | 集中的错误处理机制 |
| 难以扩展新的数据点 | 通过配置轻松添加 |

### 长期采集的考虑

对于长期采集，系统提供了：

1. **连接管理**：自动重连机制
2. **性能优化**：批量读取，减少网络开销
3. **内存管理**：避免内存泄漏
4. **错误恢复**：单个点位错误不影响其他点位

## 环境要求

### 系统依赖

```bash
# macOS
brew install cmake

# Ubuntu/Debian
sudo apt-get install cmake build-essential

# CentOS/RHEL
sudo yum install cmake gcc gcc-c++
```

### Rust依赖

```toml
[dependencies]
plctag = "0.4.1"
```

## 配置说明

### PLC连接参数

- `gateway`: PLC的IP地址
- `path`: PLC路径，通常是"1,0"
- `protocol`: 协议类型，Allen-Bradley使用"ab-eip"
- `plc`: PLC类型，如"controllogix"

### 数据类型映射

| PLC类型 | Rust类型 | 字节数 |
|---------|----------|--------|
| REAL | Float32 | 4 |
| DINT | Int32 | 4 |
| INT | Int16 | 2 |
| BOOL | Bool | 1 |

## 最佳实践

### 1. 数据点分组

将相关的数据点分组管理：

```rust
// 温度相关
collector.add_data_point(temp1_config);
collector.add_data_point(temp_array_config);

// 压力相关
collector.add_data_point(pressure1_config);
collector.add_data_point(pressure_array_config);
```

### 2. 错误处理

```rust
collector.start_polling(|data| {
    for (name, value) in data {
        match process_data_point(name, value) {
            Ok(_) => {},
            Err(e) => eprintln!("处理数据点 {} 失败: {}", name, e),
        }
    }
});
```

### 3. 性能优化

- 合理设置采集间隔（不要过于频繁）
- 对于大数组，考虑分批处理
- 使用异步版本处理高并发场景

## 故障排除

### 常见问题

1. **连接失败**：检查IP地址和PLC路径
2. **数据类型错误**：确认PLC中的数据类型与配置匹配
3. **超时**：增加timeout设置或检查网络连接

### 调试技巧

```rust
// 启用详细日志
use plctag::log::{log_adapt, set_debug_level, DebugLevel};

log_adapt();
set_debug_level(DebugLevel::Debug);
```

## 总结

这个系统的核心价值在于**统一处理**连续和非连续数据，你不需要为不同类型的数据写不同的处理逻辑。通过配置驱动的方式，可以轻松管理各种数据点，非常适合长期的工业数据采集需求。
